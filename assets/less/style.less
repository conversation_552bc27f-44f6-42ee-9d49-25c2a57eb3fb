// out: ../../style.css, compress: true, strictMath: true

/*
Theme Name: GOAT EFC
Author: <PERSON>
Version: 1.0.0
*/

@import 'vw_values.less';
@import 'constants.less';
@import 'default.less';
@import 'parts/block-utilities.less';
@import 'typo.less';
@import 'parts/header.less';
@import 'parts/footer.less';
@import 'parts/overlay.less';
@import 'parts/ddsignature.less';
@import 'parts/buttons.less';
@import 'parts/cursor.less';
@import 'parts/form.less';

// gift631 blocks (add per-block less here as they are built)
@import '../../blocks/less/gift-big-header-block.less';
@import '../../blocks/less/gift-header-block.less';
@import '../../blocks/less/gift-events-header-block.less';
@import '../../blocks/less/gift-partner-marquee-block.less';
@import '../../blocks/less/gift-signup-block.less';
@import '../../blocks/less/gift-services-block.less';
@import '../../blocks/less/gift-title-text-block.less'; 
@import '../../blocks/less/gift-sticky-big-media-block.less';
@import '../../blocks/less/gift-media-text-block.less';
@import '../../blocks/less/gift-latest-news-slider-block.less';
@import '../../blocks/less/gift-testimonials-block.less';
@import '../../blocks/less/gift-testimonial-highlight-block.less';
@import '../../blocks/less/gift-intro-text-block.less';
@import '../../blocks/less/gift-media-slider-block.less';
@import '../../blocks/less/gift-big-text-marquee-block.less';
@import '../../blocks/less/gift-timeline-block.less';
@import '../../blocks/less/gift-credentials-block.less';
@import '../../blocks/less/gift-approach-block.less'; 
@import '../../blocks/less/gift-usps-block.less';
@import '../../blocks/less/gift-media-grid-block.less';
@import '../../blocks/less/gift-ctas-block.less'; 
@import '../../blocks/less/gift-title-text-list-block.less';
@import '../../blocks/less/gift-event-promo-block.less';
@import '../../blocks/less/gift-more-brilliant-block.less';
@import '../../blocks/less/events-archive.less';
@import '../../blocks/less/gift-content-repeater-block.less';
 
@font-face {
  font-family: 'icomoon';
  src:  url('assets/fonts/icomoon.eot?twq3hi');
  src:  url('assets/fonts/icomoon.eot?twq3hi#iefix') format('embedded-opentype'),
    url('assets/fonts/icomoon.ttf?twq3hi') format('truetype'),
    url('assets/fonts/icomoon.woff?twq3hi') format('woff'),
    url('assets/fonts/icomoon.svg?twq3hi#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

// include Albra-trial font (from fonts map in assets map):
@font-face {
  font-family: 'Albra';
  src:  url('assets/fonts/Albra-Trial-Light.woff2') format('woff2'),
    url('assets/fonts/Albra-Trial-Light.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

// include Figtree font (google import) - commented out for local compilation
// @import url('https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700&display=swap');

// include font Buster Brush
@font-face {
  font-family: 'Buster Brush';
  src:  url('assets/fonts/Buster-Brush.woff2') format('woff2'),
    url('assets/fonts/Buster-Brush.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

// include Absolute Beauty
@font-face {
  font-family: 'Absolute Beauty';
  src:  url('assets/fonts/written.woff2') format('woff2'),
    url('assets/fonts/written.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
//  include Figtree black, bold, extralight, light, medium , regular
@font-face {
  font-family: 'Figtree';
  src:  url('assets/fonts/Figtree-Black.woff2') format('woff2'),
    url('assets/fonts/Figtree-Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: block;
}
@font-face {
  font-family: 'Figtree';
  src:  url('assets/fonts/Figtree-Bold.woff2') format('woff2'),
    url('assets/fonts/Figtree-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: block;
}
@font-face {
  font-family: 'Figtree';
  src:  url('assets/fonts/Figtree-ExtraLight.woff2') format('woff2'),
    url('assets/fonts/Figtree-ExtraLight.woff') format('woff');
    font-weight: 200;
  font-style: normal;
  font-display: block;
}
@font-face {
  font-family: 'Figtree';
  src:  url('assets/fonts/Figtree-Light.woff2') format('woff2'),
    url('assets/fonts/Figtree-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: block;
}
@font-face {
  font-family: 'Figtree';
  src:  url('assets/fonts/Figtree-Medium.woff2') format('woff2'),
    url('assets/fonts/Figtree-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: block;
}
@font-face {
  font-family: 'Figtree';
  src:  url('assets/fonts/Figtree-Regular.woff2') format('woff2'),
    url('assets/fonts/Figtree-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

// include

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-youtube:before {
  content: "\e912";
}
.icon-twitter:before {
  content: "\e913";
}
.icon-facebook:before {
  content: "\e910";
}
.icon-linkedin:before {
  content: "\e911";
}
.icon-instagram:before {
  content: "\e900";
}
.icon-tiktok:before {
  content: "\e901";
}
.icon-twitch:before {
  content: "\e902";
}
.icon-discord:before {
  content: "\e903";
}
.icon-futbol:before {
  content: "\e904";
}
.icon-trophy:before {
  content: "\e905";
}
.icon-headset:before {
  content: "\e906";
}
.icon-gamepad:before {
  content: "\e907";
}
.icon-arrow-right-up:before {
  content: "\e908";
}
.icon-arrow-right:before {
  content: "\e909";
}
.icon-arrow-left:before {
  content: "\e90a";
}
.icon-arrow-right-down:before {
  content: "\e90b";
}
.icon-arrow-down:before {
  content: "\e90c";
}
.icon-arrow-up:before {
  content: "\e90d";
}
.icon-phone-solid:before {
  content: "\e90e";
}
.icon-envelope:before {
  content: "\e90f";
}

::-webkit-scrollbar {
  width: @vw10;
}

::-webkit-scrollbar-track {
  background: @almostWhite;
}

::-webkit-scrollbar-thumb {
  border-radius: @vw50;
  background: rgba(0,0,0,.1);
}

.block__headline {
    padding: 20px 15px 30px;
    background: #fafafa;
    text-align: center;
}
.block__headline-title {
    font-family: 'Arial', sans-serif;
    font-size: 30px;
    font-weight: bold;
    position: relative;
}
.block__headline-title:after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background: #333;
    margin: 0 auto;
}

html.has-scroll-smooth {
	backface-visibility: hidden;
	transform: translateZ(0);
  [data-load-container] {
  	position: fixed;
  	top: 0;
  	right: 0;
  	bottom: 0;
  	left: 0;
  	width: 100vw;
  }
}

// Swup

.transition-fade {
  transition: .75s;
  opacity: 1;
}

html.is-animating .transition-fade {
  opacity: 0;
}

.grecaptcha-badge {
  visibility: hidden;
}

@media all and (max-width: 1080px) {

}

@media all and (max-width: 580px) {

}

@keyframes dash-move {
  from {
    stroke-dashoffset: 0; /* Startpositie */
  }
  to {
    stroke-dashoffset: -20; /* Naar links verschuiven */
  }
}