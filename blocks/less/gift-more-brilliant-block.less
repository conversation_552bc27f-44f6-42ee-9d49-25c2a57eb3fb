// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftMoreBrilliantBlock {
    text-align: center;
    &.inview {
        .cardsSection {
            .promiseCard,
            .benefitsCard {
                opacity: 1;
                .transform(translateY(0));
                transition: opacity .45s, transform .45s;
                -webkit-transition: opacity .45s, transform .45s;
            }
        }
    }
    .headerSection {
        margin-bottom: @vw80;
        .hugeTitle {
            margin-bottom: @vw30;
        }
        
        .subtitle {
            margin-bottom: @vw20;
        }
    }
    
    .cardsSection {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: @vw16;
        margin-bottom: @vw80;
        .promiseCard,
        .benefitsCard {
            opacity: 0;
            .transform(translateY(@vw30));
            background: @almostWhite;
            color: @secondaryColor;
            padding: @vw40;
            .rounded(@vw16);
            text-align: left;
            
            .normalTitle {
                margin-bottom: @vw25;
            }
            
            .cardList {
                list-style: none;
                padding: 0;
                margin: 0;
                
                li {
                    position: relative;
                    padding-left: @vw20;
                    margin-bottom: @vw15;
                    font-size: @vw16;
                    line-height: 1.6;
                    color: #555;
                    
                    &:before {
                        content: "•";
                        color: #C7A56F;
                        font-weight: bold;
                        position: absolute;
                        left: 0;
                        top: 0;
                    }
                    
                    &:last-child {
                        margin-bottom: 0;
                    }
                    
                    @media (max-width: 768px) {
                        font-size: @vw14;
                    }
                }
            }
        }
    }
    
    .pricingSection {
        margin-bottom: @vw60;
        
        .pricingText {
            font-size: @vw18;
            color: #C7A56F;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            
            @media (max-width: 768px) {
                font-size: @vw16;
                display: flex;
                flex-direction: column;
                gap: @vw10;
            }
            
            .fullPrice {
                color: #C7A56F;
            }
            
            .separator {
                color: #666;
                margin: 0 @vw15;
                
                @media (max-width: 768px) {
                    display: none;
                }
            }
            
            .earlyPrice {
                color: @primaryColor;
            }
        }
    }
    
    .testimonialSection {
       line-height: 1.5;
        .testimonialQuote {
            color: @almostWhite;
        }
        
        .testimonialMeta {
            color: @almostWhite;
            
            .testimonialName {
                color: @primaryColor;
            }
            
            .testimonialRole {
                font-weight: 300;
            }
            
        }
    }
}
