// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftMediaSlider {
    &.dark {
        margin-bottom: @vw100 * 4.5;
    }
    .col {
        width: 50%;
        display: inline-block;
        vertical-align: top;
    }
    .sliderWrapper {
        width: 100%;
        position: relative;
    }
    .button {
        margin-top: @vw40;
    }
    .slider {
        white-space: nowrap;
        margin: @vw70 0;
        .slide {
            display: inline-block;
            width: (@vw186 * 3) + (@vw16 * 2);
            .rounded(@vw14);
            overflow: hidden;
            margin: 0 @vw8;
            color: @almostWhite;
            text-decoration: none;
            .imageWrapper {
                height: auto;
                width: 100%;
                .transform(translate3d(0,0,0));
                .innerImage {
                    overflow: hidden;
                    position: relative;
                    .paddingRatio(590,687);
                    height: 0;
                    img, video {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        .transform(translate(-50%, -50%));
                        .transitionMore(transform, .3s);
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        object-position: center;
                    }
                }
            }
        }
    }
    .sliderIndicator {
        height: 2px;
        width: 50%;
        display: inline-block;
        margin-right: @vw40;
        background: rgba(@primaryColor, .2);
        position: relative;
        overflow: hidden;
        .innerBar {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 0%;
            background: @primaryColor;
        }
    }
}