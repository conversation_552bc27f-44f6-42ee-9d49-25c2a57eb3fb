// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftTestimonialHighlightBlock {
    &.inview {
        .textTitle {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s .9s, transform .45s .9s;
            -webkit-transition: opacity .45s .9s, transform .45s .9s;
        }
    }
    .cols {
        display: block;
        width: calc(100% ~"+" @vw40);
        margin-left: -@vw20;
        .col {
            display: inline-block;
            vertical-align: top;
            width: calc(66.6666% ~"-" @vw40);
            margin: 0 @vw20;
            &.leftCol {
                width: calc(33.3333% ~"-" @vw40);
            }
        }
    }
    .normalTitle {
        margin-bottom: @vw22;
    }
    .textTitle {
        opacity: 0;
        .transform(translateY(@vw16));
    }
    .imageWrapper {
        width: 100%;
        .innerImage {
            overflow: hidden;
            position: relative;
            .paddingRatio(1,.8);
            .rounded(@vw16);
            height: 0;
            img {
                position: absolute;
                top: 50%;
                left: 50%;
                .transform(translate(-50%, -50%));
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
    }
}
