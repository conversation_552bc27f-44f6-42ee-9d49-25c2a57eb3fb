// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftContentRepeater {
    .biggerTitle {
        text-align: center;
        margin-bottom: @vw100;
    }
    
    .contentItems {
        display: flex;
        flex-direction: column;
        gap: @vw80;
    }
    
    .contentItem {
        .normalTitle {
            margin-bottom: @vw30;
        }
        
        .text {
            margin-bottom: @vw50;
            
            p {
                margin-bottom: @vw20;
                
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
        
        .button {
            display: inline-block;
        }
    }
}
