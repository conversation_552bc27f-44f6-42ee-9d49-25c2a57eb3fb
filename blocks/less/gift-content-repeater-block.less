// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftContentRepeater {
    &.inview {
        .contentItem {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            -webkit-transition: opacity .45s, transform .45s;
            .stagger(6, 0.15s, 0.9s);
        }
    }
    .bigTitle {
        text-align: center;
        margin-bottom: @vw100;
    }
    
    .contentItems {
        display: flex;
        flex-direction: row;
        gap: @vw16;
    }
    
    .contentItem {
        padding: @vw50 @vw30;
        border: 1px solid @primaryColor;
        opacity: 0;
        .transform(translateY(@vw16));
        .rounded(@vw14);
        .normalTitle {
            margin-bottom: @vw30;
        }
        .text {
            margin-bottom: @vw50;
            p {
                margin-bottom: @vw20;
                &:last-child {
                    margin-bottom: 0;
                }
                strong {
                    text-transform: uppercase;
                    letter-spacing: .14em;
                    font-weight: 300;
                    color: rgba(@secondaryColor, .5);
                }
            }
        }
        
        .button {
            display: inline-block;
        }
    }
}
